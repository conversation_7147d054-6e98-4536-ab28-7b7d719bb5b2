name: build

permissions: # Principle of least privilege
  contents: read
  actions: read

on:
  push:
    branches: [master, nightly, develop]
  pull_request:
    branches: ['*']

jobs:
  pre-commit:
    runs-on: ubuntu-22.04 # (glibc 2.35) wider runtime range than 24.04/glibc 2.39
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          python-version: "3.13"
          free-disk-space: "true"

      - name: Run pre-commit
        run: pre-commit run --all-files

  build-linux-x86:
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-22.04 # (glibc 2.35) wider runtime range than 24.04/glibc 2.39
        python-version:
          - "3.11"
          - "3.12"
          - "3.13"
    defaults:
      run:
        shell: bash
    name: build - python ${{ matrix.python-version }} (${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    needs:
      - pre-commit
    env:
      BUILD_MODE: release
      RUST_BACKTRACE: 1
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DB: nautilus
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: ${{ matrix.python-version }}
          free-disk-space: "true"

      - name: Install Nautilus CLI and run init postgres
        run: |
          make install-cli
          nautilus database init --schema ${{ github.workspace }}/schema/sql
        env:
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USERNAME: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DATABASE: nautilus

      - name: Cached test data
        uses: ./.github/actions/common-test-data

      - name: Run Rust tests
        run: make cargo-test

      - name: Build and install wheel
        uses: ./.github/actions/common-wheel-build
        with:
          python-version: ${{ matrix.python-version }}
          github_ref: ${{ github.ref }}

      - name: Run tests
        run: |
          uv run --no-sync pytest --ignore=tests/performance_tests

      - name: Upload wheel artifact
        uses: ./.github/actions/upload-artifact-wheel

  build-linux-arm:
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-22.04-arm
        python-version:
          - "3.11"
          - "3.12"
          - "3.13"
    defaults:
      run:
        shell: bash
    name: build - python ${{ matrix.python-version }} (${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    # Pause job on develop branch for now (takes ~1-1.5 hrs)
    if: >
      !( (github.event_name == 'push' && github.ref_name == 'develop')
      || (github.event_name == 'pull_request' && github.base_ref == 'develop') )
    needs:
      - pre-commit
    env:
      BUILD_MODE: release
      RUST_BACKTRACE: 1
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DB: nautilus
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: ${{ matrix.python-version }}
          free-disk-space: "true"

      - name: Install Nautilus CLI and run init postgres
        run: |
          make install-cli
          nautilus database init --schema ${{ github.workspace }}/schema/sql
        env:
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USERNAME: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DATABASE: nautilus

      - name: Cached test data
        uses: ./.github/actions/common-test-data

      - name: Run Rust tests
        run: make cargo-test

      - name: Build and install wheel
        uses: ./.github/actions/common-wheel-build
        with:
          python-version: ${{ matrix.python-version }}
          github_ref: ${{ github.ref }}

      - name: Run tests
        run: |
          uv run --no-sync pytest --ignore=tests/performance_tests

      - name: Upload wheel artifact
        uses: ./.github/actions/upload-artifact-wheel

  build-macos:
    strategy:
      fail-fast: false
      matrix:
        os:
          - macos-latest
        python-version:
          - "3.11"
          - "3.12"
          - "3.13"
    defaults:
      run:
        shell: bash
    name: build - python ${{ matrix.python-version }} (${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    # Pause job on develop branch for now (takes ~1-1.5 hrs)
    if: >
      !( (github.event_name == 'push' && github.ref_name == 'develop')
      || (github.event_name == 'pull_request' && github.base_ref == 'develop') )
    needs:
      - pre-commit
    env:
      BUILD_MODE: release
      RUST_BACKTRACE: 1
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cached test data
        uses: ./.github/actions/common-test-data

      - name: Run Rust tests
        run: make cargo-test

      - name: Build and install wheel
        uses: ./.github/actions/common-wheel-build
        with:
          python-version: ${{ matrix.python-version }}
          github_ref: ${{ github.ref }}

      - name: Run tests
        run: |
          uv run --no-sync pytest --ignore=tests/performance_tests

      - name: Upload wheel artifact
        uses: ./.github/actions/upload-artifact-wheel

  build-windows:
    strategy:
      fail-fast: false
      matrix:
        os:
          - windows-latest
        python-version:
          - "3.11"
          - "3.12"
          - "3.13.2" # pinned to 3.13.2, see https://github.com/nautechsystems/nautilus_trader/blob/develop/docs/getting_started/installation.md
    defaults:
      run:
        shell: bash
    name: build - python ${{ matrix.python-version }} (${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    # Pause job on develop branch for now (takes ~1 hr)
    if: >
      !( (github.event_name == 'push' && github.ref_name == 'develop')
      || (github.event_name == 'pull_request' && github.base_ref == 'develop') )
    needs:
      - pre-commit
    env:
      BUILD_MODE: release
      HIGH_PRECISION: false
      PARALLEL_BUILD: false
      RUST_BACKTRACE: 1
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: ${{ matrix.python-version }}
          free-disk-space: "true"

      - name: Build and install wheel
        uses: ./.github/actions/common-wheel-build
        with:
          python-version: ${{ matrix.python-version }}
          github_ref: ${{ github.ref }}

      - name: Cached test data
        uses: ./.github/actions/common-test-data

      - name: Install test dependencies
        run: make install-just-deps

      - name: Run tests
        run: |
          uv run --no-sync python -m pytest --ignore=tests/performance_tests

      - name: Upload wheel artifact
        uses: ./.github/actions/upload-artifact-wheel

  publish-wheels-develop:
    name: publish-wheels-develop
    runs-on: ubuntu-latest
    permissions:
      actions: write # Required for deleting artifacts
      contents: read
    needs:
      - build-linux-x86
      # - build-linux-arm # Reinstate this when/if linux-arm runs on every build
      # - build-macos # Reinstate this when/if macos runs on every build
      # - build-windows # Reinstate this when/if windows runs on every build
    if: >
      github.event_name == 'push' && github.ref == 'refs/heads/develop'
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      CLOUDFLARE_R2_URL: ${{ secrets.CLOUDFLARE_R2_URL }}
      CLOUDFLARE_R2_BUCKET_NAME: "packages"
      CLOUDFLARE_R2_REGION: "auto"
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Publish wheels to Cloudflare R2
        uses: ./.github/actions/publish-wheels

      - name: Fetch and delete artifacts for current run
        shell: bash
        run: |
          bash ./scripts/ci/publish-wheels-delete-artifacts.sh

  publish-wheels-nightly:
    runs-on: ubuntu-latest
    permissions:
      actions: write # Required for deleting artifacts
      contents: read
    needs:
      - build-linux-x86
      - build-linux-arm
      - build-macos
      # - build-windows
    if: >
      github.event_name == 'push' && github.ref == 'refs/heads/nightly'
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      CLOUDFLARE_R2_URL: ${{ secrets.CLOUDFLARE_R2_URL }}
      CLOUDFLARE_R2_BUCKET_NAME: "packages"
      CLOUDFLARE_R2_REGION: "auto"
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Publish wheels to Cloudflare R2
        uses: ./.github/actions/publish-wheels

      - name: Fetch and delete artifacts for current run
        shell: bash
        run: |
          bash ./scripts/ci/publish-wheels-delete-artifacts.sh

  publish-wheels-master:
    runs-on: ubuntu-latest
    permissions:
      contents: write # Required for uploading release assets
      actions: write  # Required for deleting artifacts
    needs:
      - build-linux-x86
      - build-linux-arm
      - build-macos
      - build-windows
      - tag-release
    if: >
      github.event_name == 'push' && github.ref == 'refs/heads/master'
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      CLOUDFLARE_R2_URL: ${{ secrets.CLOUDFLARE_R2_URL }}
      CLOUDFLARE_R2_BUCKET_NAME: "packages"
      CLOUDFLARE_R2_REGION: "auto"
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Get uv version
        shell: bash
        run: |
          echo "UV_VERSION=$(cat uv-version)" >> $GITHUB_ENV

      - name: Install uv
        uses: astral-sh/setup-uv@bd01e18f51369d5a26f1651c3cb451d3417e3bba # v6.3.1
        with:
          version: ${{ env.UV_VERSION }}

      - name: Publish wheels to Cloudflare R2
        uses: ./.github/actions/publish-wheels

      - name: Download built wheels
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          path: dist/
          pattern: "*.whl"

      - name: Upload wheels to GitHub release
        run: |
          find dist -type f -name "*.whl" | while read wheel; do
            echo "Uploading $wheel to release"
            asset_name=$(basename "$wheel")
            curl -X POST \
              -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              -H "Content-Type: application/octet-stream" \
              --data-binary @"$wheel" \
              "${{ needs.tag-release.outputs.upload_url }}?name=$asset_name"
          done

      - name: Consolidate wheels for PyPI
        shell: bash
        run: |
          set -euo pipefail

          # Create dist/all directory for consolidated wheels
          mkdir -p dist/all

          # Move all .whl files from subdirectories to dist/all (exclude dist/all itself)
          find dist/ -path dist/all -prune -o -name "*.whl" -type f -exec mv {} dist/all/ \;

          echo "Consolidated wheels in dist/all:"
          ls -la dist/all/

      - name: Publish to PyPI
        if: success()
        env:
          UV_PUBLISH_USERNAME: ${{ secrets.PYPI_USERNAME }}
          UV_PUBLISH_PASSWORD: ${{ secrets.PYPI_TOKEN }}
        run: |
          uv publish dist/all

      - name: Fetch and delete artifacts for current run
        shell: bash
        run: |
          bash ./scripts/ci/publish-wheels-delete-artifacts.sh

  tag-release:
    needs:
      - build-linux-x86
      - build-linux-arm
      - build-macos
      - build-windows
    permissions:
      contents: write # Required for pushing tags and upload release assets
      actions: write  # Required for creating releases
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create-release.outputs.upload_url }}
      tag_name: ${{ env.TAG_NAME }}
    steps:
      # Security hardening
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: "3.13"
          free-disk-space: "true"

      - name: Ensure script is executable
        run: chmod +x scripts/package-version.sh

      - name: Create git tag
        # https://github.com/salsify/action-detect-and-tag-new-version
        uses: salsify/action-detect-and-tag-new-version@b1778166f13188a9d478e2d1198f993011ba9864 # v2.0.3
        with:
          version-command: ./scripts/package-version.sh

      - name: Set output
        id: vars
        run: |
          echo "TAG_NAME=v$(./scripts/package-version.sh)" >> $GITHUB_ENV
          echo "RELEASE_NAME=NautilusTrader $(./scripts/package-version.sh) Beta" >> $GITHUB_ENV
          sed -n '/^#/,${p;/^---/q};w RELEASE.md' RELEASES.md

      - name: Create GitHub release
        id: create-release
        # https://github.com/actions/create-release # v1.1.4
        uses: actions/create-release@0cb9c9b65d5d1901c1f53e5e66eaf4afd303e70e
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ env.TAG_NAME }}
          release_name: ${{ env.RELEASE_NAME }}
          draft: false
          prerelease: false
          body_path: RELEASE.md

  publish-sdist:
    needs: [tag-release]
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    permissions:
      contents: write # Required for uploading release assets
    env:
      COPY_TO_SOURCE: false # Do not copy built *.so files back into source tree
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: "3.13"
          free-disk-space: "true"

      - name: Build sdist
        run: |
          uv build --sdist

      - name: Set release output
        id: vars
        run: |
          if [ ! -d "./dist" ]; then
            echo "Error: dist directory not found"
            exit 1
          fi

          ASSET_PATH=$(find ./dist -name "*.tar.gz" -type f | xargs ls -t 2>/dev/null | head -n 1)

          if [ -z "$ASSET_PATH" ]; then
            echo "Error: No .tar.gz files found in dist directory"
            exit 1
          fi

          echo "ASSET_PATH=$ASSET_PATH" >> $GITHUB_ENV
          echo "ASSET_NAME=$(basename "$ASSET_PATH")" >> $GITHUB_ENV

      - name: Upload release asset
        id: upload-release-asset
        # https://github.com/actions/upload-release-asset
        uses: actions/upload-release-asset@ef2adfe8cb8ebfa540930c452c576b3819990faa # v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.tag-release.outputs.upload_url }}
          asset_path: ${{ env.ASSET_PATH }}
          asset_name: ${{ env.ASSET_NAME }}
          asset_content_type: application/gzip

      - name: Publish to PyPI
        if: success()
        env:
          UV_PUBLISH_USERNAME: ${{ secrets.PYPI_USERNAME }}
          UV_PUBLISH_PASSWORD: ${{ secrets.PYPI_TOKEN }}
        run: |
          uv publish dist

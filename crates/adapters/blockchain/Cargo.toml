[package]
name = "nautilus-blockchain"
readme = "README.md"
publish = false  # Do not publish to crates.io for now
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_blockchain"
crate-type = ["rlib", "cdylib"]

[features]
default = ["defi"]
hypersync = ["hypersync-client", "hypersync-schema"]
python = ["pyo3", "nautilus-network/python"]
defi = [
  "nautilus-common/defi",
  "nautilus-data/defi",
  "nautilus-live/defi",
  "nautilus-model/defi",
]

[package.metadata.docs.rs]
features = ["defi", "python"]
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true, features = ["defi"] }
nautilus-core = { workspace = true }
nautilus-data = { workspace = true, features = ["defi"] }
nautilus-infrastructure = { workspace = true, features = ["postgres"] }
nautilus-live = { workspace = true }
nautilus-model = { workspace = true, features = ["defi"] }
nautilus-network = { workspace = true }
nautilus-system = { workspace = true }

ahash = { workspace = true }
alloy = { workspace = true }
anyhow = { workspace = true }
async-stream = { workspace = true }
async-trait = { workspace = true }
bytes = { workspace = true }
dotenvy = { workspace = true }
enum_dispatch = { workspace = true }
futures-util = { workspace = true }
hex = { workspace = true }
hypersync-client = { workspace = true, optional = true }
hypersync-schema = { workspace = true, optional = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
reqwest = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
sqlx = { workspace = true }
strum = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tokio-tungstenite = { workspace = true }
tracing = { workspace = true }
ustr = { workspace = true }

[dev-dependencies]
rstest = { workspace = true }

[[bin]]
name = "live_blocks_rpc"
path = "bin/watch_rpc_live_blocks.rs"
required-features = ["defi", "hypersync"]

[[bin]]
name = "live_blocks_hypersync"
path = "bin/watch_hypersync_live_blocks.rs"
required-features = ["defi", "hypersync"]

[[bin]]
name = "sync_tokens_pools"
path = "bin/sync_tokens_pools.rs"
required-features = ["defi", "hypersync"]

[[bin]]
name = "sync_pool_events"
path = "bin/sync_pool_events.rs"
required-features = ["defi", "hypersync"]

[[bin]]
name = "node_test"
path = "bin/node_test.rs"
required-features = ["defi", "hypersync"]

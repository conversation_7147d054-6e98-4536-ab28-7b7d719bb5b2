[package]
name = "nautilus-tardis"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_tardis"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
  "nautilus-serialization/extension-module",
]
python = [
  "pyo3",
  "pyo3-async-runtimes",
  "nautilus-core/python",
  "nautilus-model/python",
  "nautilus-serialization/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true }
nautilus-model = { workspace = true, features = ["python"] }
nautilus-serialization = { workspace = true }

anyhow = { workspace = true }
arrow = { workspace = true }
async-stream = { workspace = true }
chrono = { workspace = true }
csv = { workspace = true }
derive_builder = { workspace = true }
flate2 = { workspace = true }
futures-util = { workspace = true }
heck = { workspace = true }
parquet = { workspace = true }
pyo3 = { workspace = true, optional = true }
pyo3-async-runtimes = { workspace = true, optional = true }
reqwest = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
ryu = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
strum = { workspace = true }
thiserror = { workspace = true }
thousands = { workspace = true }
tokio = { workspace = true }
tokio-tungstenite = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
urlencoding = { workspace = true }
ustr = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
nautilus-testkit = { workspace = true }
rstest = { workspace = true }
tracing-test = { workspace = true }

[[bin]]
name = "tardis-csv"
path = "bin/example_csv.rs"

[[bin]]
name = "tardis-http"
path = "bin/example_http.rs"

[[bin]]
name = "tardis-replay"
path = "bin/example_replay.rs"

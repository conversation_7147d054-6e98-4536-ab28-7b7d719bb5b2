[package]
name = "nautilus-core"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_core"
crate-type = ["rlib", "staticlib"]

[features]
default = []
extension-module = ["pyo3/extension-module"]
ffi = ["cbindgen"]
python = ["pyo3", "strum"]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
ahash = { workspace = true }
anyhow = { workspace = true }
bytes = { workspace = true }
chrono = { workspace = true }
heck = { workspace = true }
indexmap = { workspace = true }
pyo3 = { workspace = true, optional = true }
rand = { workspace = true }
rmp-serde = { workspace = true }
rust_decimal = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
strum = { workspace = true, optional = true }
ustr = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
iai = { workspace = true }
proptest = { workspace = true }
rstest = { workspace = true }

[build-dependencies]
cbindgen = { workspace = true, optional = true }
toml = { workspace = true }

[[bench]]
name = "correctness"
path = "benches/correctness.rs"
harness = false

[[bench]]
name = "time"
path = "benches/time.rs"
harness = false

[[bench]]
name = "uuid"
path = "benches/uuid.rs"
harness = false
